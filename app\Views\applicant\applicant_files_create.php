<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1">Upload New File</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/dashboard') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('applicant/profile#files') ?>">Profile</a></li>
                            <li class="breadcrumb-item active">Upload File</li>
                        </ol>
                    </nav>
                </div>
                <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                </a>
            </div>

            <!-- Upload Form Card -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>Upload Document
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Display validation errors -->
                            <?php if (session()->getFlashdata('errors')): ?>
                                <div class="alert alert-danger">
                                    <ul class="mb-0">
                                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                            <li><?= esc($error) ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Display success message -->
                            <?php if (session()->getFlashdata('success')): ?>
                                <div class="alert alert-success">
                                    <?= session()->getFlashdata('success') ?>
                                </div>
                            <?php endif; ?>

                            <!-- Display error message -->
                            <?php if (session()->getFlashdata('error')): ?>
                                <div class="alert alert-danger">
                                    <?= session()->getFlashdata('error') ?>
                                </div>
                            <?php endif; ?>

                            <form action="<?= base_url('applicant/profile/files/store') ?>" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <?= csrf_field() ?>
                                
                                <!-- Upload Process Steps -->
                                <div class="row g-3">
                                    <div class="col-12">
                                        <div class="upload-steps" id="uploadSteps">
                                            <h6 class="mb-3 text-muted">Process Steps</h6>
                                            <div class="steps-container">
                                                <div class="step-item" id="step1">
                                                    <span class="step-text">1. <i class="fas fa-circle step-icon text-muted"></i> Select File: Choose PDF document</span>
                                                </div>

                                                <div class="step-item" id="step2">
                                                    <span class="step-text">2. <i class="fas fa-circle step-icon text-muted"></i> Process Pages: Split and convert to images</span>
                                                </div>

                                                <div class="step-item" id="step3">
                                                    <span class="step-text">3. <i class="fas fa-circle step-icon text-muted"></i> Extract Text: AI text extraction</span>
                                                </div>

                                                <div class="step-item" id="step4">
                                                    <span class="step-text">4. <i class="fas fa-circle step-icon text-muted"></i> Upload: Save to profile</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- File Title and Description (Hidden initially) -->
                                    <div class="col-12" id="titleDescriptionSection" style="display: none;">
                                        <div class="card border-success">
                                            <div class="card-header bg-success text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-edit me-2"></i>File Information
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row g-3">
                                                    <div class="col-12">
                                                        <label class="form-label">File Title <span class="text-danger">*</span></label>
                                                        <input type="text" class="form-control" name="file_title" value="<?= old('file_title') ?>" required>
                                                        <div class="invalid-feedback">
                                                            Please provide a file title.
                                                        </div>
                                                        <div class="form-text">
                                                            AI-generated title based on document content (you can edit this)
                                                        </div>
                                                    </div>

                                                    <div class="col-12">
                                                        <label class="form-label">File Description</label>
                                                        <textarea class="form-control" name="file_description" rows="3" placeholder="AI-generated description..."><?= old('file_description') ?></textarea>
                                                        <div class="form-text">
                                                            AI-generated description based on document content (you can edit this)
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-12">
                                        <label class="form-label">Select PDF File <span class="text-danger">*</span></label>
                                        <div class="custom-file-input-wrapper">
                                            <div class="custom-file-input" id="customFileInput">
                                                <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                                <p class="mb-2"><strong>Choose PDF Document</strong></p>
                                                <p class="text-muted small mb-3">Click to select a PDF file from your computer</p>
                                                <button type="button" class="btn btn-primary" id="selectFileBtn">
                                                    <i class="fas fa-folder-open me-2"></i>Browse Files
                                                </button>
                                                <input type="file" id="pdfFileInput" accept=".pdf" style="display: none;">
                                            </div>
                                            <div class="file-info mt-3" id="fileInfo" style="display: none;">
                                                <div class="card border-info">
                                                    <div class="card-header bg-info text-white">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-file-pdf me-2"></i>File Information
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row g-3">
                                                            <div class="col-md-6">
                                                                <div class="info-item">
                                                                    <label class="info-label">File Name:</label>
                                                                    <span class="info-value" id="fileName"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="info-item">
                                                                    <label class="info-label">File Size:</label>
                                                                    <span class="info-value" id="fileSize"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="info-item">
                                                                    <label class="info-label">Total Pages:</label>
                                                                    <span class="info-value" id="totalPages"></span>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="info-item">
                                                                    <label class="info-label">Split Sets:</label>
                                                                    <span class="info-value" id="splitSets">-</span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Processing Status -->
                                                        <div class="mt-3" id="processingStatus" style="display: none;">
                                                            <hr>
                                                            <h6 class="text-muted mb-3">Processing Status</h6>
                                                            <div class="row g-2">
                                                                <div class="col-md-6">
                                                                    <div class="status-item">
                                                                        <i class="fas fa-cut me-2 text-muted" id="splitIcon"></i>
                                                                        <span class="status-text" id="splitStatus">PDF Splitting: Pending</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="status-item">
                                                                        <i class="fas fa-image me-2 text-muted" id="imageIcon"></i>
                                                                        <span class="status-text" id="imageStatus">Image Conversion: Pending</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="status-item">
                                                                        <i class="fas fa-robot me-2 text-muted" id="aiIcon"></i>
                                                                        <span class="status-text" id="aiStatus">AI Text Extraction: Pending</span>
                                                                    </div>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <div class="status-item">
                                                                        <i class="fas fa-check-circle me-2 text-muted" id="completeIcon"></i>
                                                                        <span class="status-text" id="completeStatus">Processing: Not Started</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>Only PDF files are supported. Maximum size: 25MB
                                        </div>
                                    </div>

                                    <!-- AI Processing Section -->
                                    <div class="col-12" id="aiProcessingSection" style="display: none;">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-robot me-2"></i>AI Text Extraction
                                                </h6>
                                            </div>
                                            <div class="card-body">
                                                <button type="button" class="btn btn-primary" id="processBtn" onclick="processFile()" disabled>
                                                    <i class="fas fa-magic me-2"></i>Converting pages to images...
                                                </button>

                                                <!-- Progress Section -->
                                                <div class="progress-section mt-3" id="progressSection" style="display: none;">
                                                    <div class="status-text mb-2" id="statusText">Processing...</div>
                                                    <div class="progress mb-2">
                                                        <div class="progress-bar" id="progressFill" style="width: 0%"></div>
                                                    </div>
                                                    <div class="timing-info" id="timingInfo">
                                                        <small class="text-muted">
                                                            <strong>Elapsed:</strong> <span id="elapsedTime">0s</span> |
                                                            <strong>Status:</strong> <span id="currentStatus">Starting...</span>
                                                        </small>
                                                    </div>
                                                </div>

                                                <!-- Extracted Text Display -->
                                                <div class="extracted-text-section mt-3" id="extractedTextSection" style="display: none;">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-file-alt me-2"></i>Extracted Text
                                                        </h6>
                                                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#extractedTextCollapse">
                                                            <i class="fas fa-eye me-1"></i>View Text
                                                        </button>
                                                    </div>
                                                    <div class="collapse" id="extractedTextCollapse">
                                                        <div class="card card-body bg-light">
                                                            <pre id="extractedTextContent" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap; font-size: 12px;"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Final Upload Section -->
                                <div class="col-12" id="finalUploadSection" style="display: none;">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-check-circle me-2"></i>Ready to Upload
                                        </h6>
                                        <p class="mb-0">Text extraction completed successfully! You can now upload the file to your profile.</p>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between mt-4">
                                    <a href="<?= base_url('applicant/profile#files') ?>" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="uploadFileBtn" style="display: none;">
                                        <i class="fas fa-upload me-2"></i>Upload File to Profile
                                    </button>
                                </div>

                                <!-- Hidden fields for extracted data -->
                                <input type="hidden" name="extracted_text" id="hiddenExtractedText">
                                <input type="hidden" name="file_data" id="hiddenFileData">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.custom-file-input-wrapper {
    margin-bottom: 1rem;
}

.custom-file-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.custom-file-input:hover {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.upload-steps {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.step-item {
    padding: 8px 0;
    transition: all 0.2s ease;
}

.step-text {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.step-item.active .step-text {
    color: #dc3545;
}

.step-item.completed .step-text {
    color: #198754;
}

.step-icon {
    margin: 0 8px;
    font-size: 12px;
}

.step-item.active .step-icon {
    color: #dc3545 !important;
    animation: pulse 1.5s infinite;
}

.step-item.completed .step-icon {
    color: #198754 !important;
}

.step-item.completed .step-icon:before {
    content: "\f00c"; /* FontAwesome check mark */
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.progress-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
}

.timing-info {
    font-size: 14px;
}

.extracted-text-section {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-left: 10px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* File Information Styles */
.info-item {
    margin-bottom: 8px;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.info-value {
    color: #212529;
    font-size: 14px;
    margin-left: 8px;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    font-size: 13px;
}

.status-text {
    color: #6c757d;
}

.status-item.processing .status-text {
    color: #dc3545;
    font-weight: 500;
}

.status-item.completed .status-text {
    color: #198754;
    font-weight: 500;
}

.status-item.processing i {
    color: #dc3545 !important;
    animation: pulse 1.5s infinite;
}

.status-item.completed i {
    color: #198754 !important;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script src="https://unpkg.com/pdf-lib@1.17.1/dist/pdf-lib.min.js"></script>
<script>
// Gemini AI Configuration
const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
const GEMINI_MODEL = 'gemini-2.0-flash';
const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

let selectedFile = null;
let pdfDocument = null;
let totalPages = 0;
let extractedText = '';
let processingStartTime = null;
let timingInterval = null;
let convertedPageSets = []; // Store pre-converted page images organized by sets
let allPagesConverted = false;

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

$(document).ready(function() {
    // File input handling - single event listener to prevent double dialogue
    document.getElementById('pdfFileInput').addEventListener('change', handleFileSelect);

    // Button click handling - prevent multiple event listeners
    document.getElementById('selectFileBtn').addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('pdfFileInput').click();
    });

    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        event.preventDefault();

        if (!extractedText) {
            alert('Please process the PDF file first to extract text.');
            return;
        }

        // Mark step 4 as completed
        markStepCompleted(4);

        // Set hidden fields
        document.getElementById('hiddenExtractedText').value = extractedText;
        document.getElementById('hiddenFileData').value = JSON.stringify({
            name: selectedFile.name,
            size: selectedFile.size,
            pages: totalPages
        });

        // Show uploading status
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';

        // Submit form normally
        this.submit();
    });
});

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
        handleFile(file);
    } else {
        alert('Please select a valid PDF file.');
    }
}

async function handleFile(file) {
    selectedFile = file;

    // Check file size (25MB = 25 * 1024 * 1024 bytes)
    const maxSize = 25 * 1024 * 1024;
    if (file.size > maxSize) {
        alert('File size must be less than 25MB');
        return;
    }

    try {
        // Load PDF to get page count
        const arrayBuffer = await file.arrayBuffer();
        pdfDocument = await pdfjsLib.getDocument(arrayBuffer).promise;
        totalPages = pdfDocument.numPages;

        // Calculate split sets (5 pages per set)
        const splitSets = Math.ceil(totalPages / 5);

        // Update file information
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        document.getElementById('totalPages').textContent = totalPages;
        document.getElementById('splitSets').textContent = `${splitSets} sets (5 pages each)`;

        // Show file info and processing status
        document.getElementById('fileInfo').style.display = 'block';
        document.getElementById('processingStatus').style.display = 'block';
        document.getElementById('aiProcessingSection').style.display = 'block';

        // Reset all status indicators
        resetProcessingStatus();

        // Mark step 1 as completed and activate step 2
        markStepCompleted(1);
        activateStep(2);

        showSuccess(`PDF loaded successfully! ${totalPages} pages found, will be split into ${splitSets} sets. Starting immediate processing...`);

        // Immediately start PDF splitting and image conversion
        await convertAllPagesToImages();

    } catch (error) {
        showError('Error loading PDF: ' + error.message);
    }
}

async function convertAllPagesToImages() {
    try {
        // Update status to show conversion starting
        updateProcessingStatus('split', 'processing', 'PDF Splitting: Starting...');
        updateProcessingStatus('image', 'processing', 'Image Conversion: Starting...');

        const totalSets = Math.ceil(totalPages / 5);
        convertedPageSets = [];

        for (let setIndex = 0; setIndex < totalSets; setIndex++) {
            const startPage = setIndex * 5 + 1;
            const endPage = Math.min((setIndex + 1) * 5, totalPages);

            // Update status for current set
            updateProcessingStatus('split', 'processing', `PDF Splitting: Set ${setIndex + 1}/${totalSets} (Pages ${startPage}-${endPage})`);
            updateProcessingStatus('image', 'processing', `Image Conversion: Set ${setIndex + 1}/${totalSets} (Pages ${startPage}-${endPage})`);

            // Convert pages in this set to images
            const setImages = [];
            for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
                try {
                    const page = await pdfDocument.getPage(pageNum);
                    const scale = 2.0; // High resolution for better text recognition
                    const viewport = page.getViewport({ scale });

                    // Create canvas for image conversion
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Render PDF page to canvas
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert to base64 PNG image
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    setImages.push({
                        pageNumber: pageNum,
                        data: imageData
                    });

                } catch (error) {
                    console.error(`Error converting page ${pageNum}:`, error);
                    // Add placeholder for failed page
                    setImages.push({
                        pageNumber: pageNum,
                        data: null,
                        error: error.message
                    });
                }
            }

            // Store the converted set
            convertedPageSets.push({
                setNumber: setIndex + 1,
                startPage: startPage,
                endPage: endPage,
                images: setImages
            });

            // Small delay to prevent UI blocking
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Mark splitting and conversion as completed
        updateProcessingStatus('split', 'completed', 'PDF Splitting: All sets completed');
        updateProcessingStatus('image', 'completed', 'Image Conversion: All pages converted to images');

        // Mark step 2 as completed and activate step 3
        markStepCompleted(2);
        activateStep(3);

        // Set flag and enable processing
        allPagesConverted = true;
        document.getElementById('processBtn').disabled = false;
        document.getElementById('processBtn').innerHTML = '<i class="fas fa-magic me-2"></i>Extract Text with AI';

        showSuccess(`All ${totalPages} pages converted to images successfully! Ready for AI text extraction.`);

    } catch (error) {
        showError('Error converting pages to images: ' + error.message);
        updateProcessingStatus('split', 'error', 'PDF Splitting: Failed');
        updateProcessingStatus('image', 'error', 'Image Conversion: Failed');
    }
}

async function processFile() {
    if (!selectedFile || !pdfDocument || !allPagesConverted) {
        alert('Please wait for page conversion to complete first.');
        return;
    }

    // Initialize timing
    processingStartTime = Date.now();
    extractedText = '';

    // Show progress section and update button
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('processBtn').disabled = true;
    document.getElementById('processBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

    // Start timing updates
    startTimingUpdates();

    try {
        // Mark step 3 as active for AI processing
        activateStep(3);

        // Update AI processing status
        updateProcessingStatus('ai', 'processing', 'AI Text Extraction: Starting...');

        const totalSets = convertedPageSets.length;

        for (let setIndex = 0; setIndex < totalSets; setIndex++) {
            const pageSet = convertedPageSets[setIndex];
            const { setNumber, startPage, endPage, images } = pageSet;

            // Update progress
            const overallProgress = (setIndex / totalSets) * 100;
            document.getElementById('progressFill').style.width = overallProgress + '%';
            document.getElementById('statusText').textContent =
                `Processing set ${setNumber} of ${totalSets} (Pages ${startPage}-${endPage})`;
            document.getElementById('currentStatus').textContent =
                `AI extracting text from pages ${startPage}-${endPage}...`;

            // Update AI processing status
            updateProcessingStatus('ai', 'processing', `AI Text Extraction: Set ${setNumber}/${totalSets} (Pages ${startPage}-${endPage})`);

            // Send pre-converted images to Gemini AI
            const setText = await extractTextWithGemini(images, startPage, endPage);

            // Append to full text
            if (setText) {
                extractedText += `\n\n## Pages ${startPage}-${endPage}\n\n${setText}`;
            }

            // Small delay to prevent rate limiting
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Mark AI processing as completed
        updateProcessingStatus('ai', 'completed', 'AI Text Extraction: All sets completed');

        // Stop timing updates
        stopTimingUpdates();

        // Mark step 3 as completed
        markStepCompleted(3);

        // Generate title and description using AI
        await generateTitleAndDescription();

        // Show title and description section
        document.getElementById('titleDescriptionSection').style.display = 'block';

        // Show final results
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('extractedTextContent').textContent = extractedText;
        document.getElementById('extractedTextSection').style.display = 'block';
        document.getElementById('finalUploadSection').style.display = 'block';
        document.getElementById('uploadFileBtn').style.display = 'inline-block';

        // Activate step 4 (Upload File)
        activateStep(4);

        // Mark processing as completed
        updateProcessingStatus('complete', 'completed', 'Processing: All steps completed successfully');

        document.getElementById('processBtn').disabled = false;
        document.getElementById('processBtn').innerHTML = '<i class="fas fa-magic me-2"></i>Extract Text with AI';

        const totalTime = Date.now() - processingStartTime;
        showSuccess(`Text extraction completed successfully! Total time: ${formatTime(totalTime)}. You can now review and upload the file.`);

    } catch (error) {
        stopTimingUpdates();
        showError('Error processing PDF: ' + error.message);
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('processBtn').disabled = false;
        document.getElementById('processBtn').innerHTML = '<i class="fas fa-magic me-2"></i>Extract Text with AI';
    }
}

// Split PDF into chunks using PDF-lib
async function splitPdfIntoChunks(file, pagesPerChunk) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const pdfDoc = await PDFLib.PDFDocument.load(arrayBuffer);
        const totalPages = pdfDoc.getPageCount();
        const chunks = [];

        for (let i = 0; i < totalPages; i += pagesPerChunk) {
            const startPage = i + 1;
            const endPage = Math.min(i + pagesPerChunk, totalPages);

            // Create new PDF document for this chunk
            const chunkDoc = await PDFLib.PDFDocument.create();

            // Copy pages to chunk document
            const pageIndices = [];
            for (let j = i; j < Math.min(i + pagesPerChunk, totalPages); j++) {
                pageIndices.push(j);
            }

            const copiedPages = await chunkDoc.copyPages(pdfDoc, pageIndices);
            copiedPages.forEach((page) => chunkDoc.addPage(page));

            // Serialize the chunk PDF
            const pdfBytes = await chunkDoc.save();

            chunks.push({
                startPage: startPage,
                endPage: endPage,
                pdfBytes: pdfBytes
            });
        }

        return chunks;
    } catch (error) {
        console.error('Error splitting PDF:', error);
        // Fallback to original method if PDF-lib fails
        return await splitPdfFallback(file, pagesPerChunk);
    }
}

// Fallback method using PDF.js only
async function splitPdfFallback(file, pagesPerChunk) {
    const arrayBuffer = await file.arrayBuffer();
    const pdfDoc = await pdfjsLib.getDocument(arrayBuffer).promise;
    const totalPages = pdfDoc.numPages;
    const chunks = [];

    for (let i = 0; i < totalPages; i += pagesPerChunk) {
        const startPage = i + 1;
        const endPage = Math.min(i + pagesPerChunk, totalPages);

        chunks.push({
            startPage: startPage,
            endPage: endPage,
            pdfBytes: arrayBuffer // Use original PDF for fallback
        });
    }

    return chunks;
}

// Convert PDF chunk to images using PDF.js
async function convertPdfChunkToImages(pdfBytes, startPage, endPage) {
    const images = [];

    try {
        // Load the PDF chunk with PDF.js
        const pdfDoc = await pdfjsLib.getDocument({ data: pdfBytes }).promise;

        // For fallback case, we need to extract specific pages
        const isFullPdf = pdfDoc.numPages > (endPage - startPage + 1);

        if (isFullPdf) {
            // Extract specific pages from full PDF
            for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
                try {
                    const page = await pdfDoc.getPage(pageNum);
                    const scale = 2.0; // Higher scale for better text recognition
                    const viewport = page.getViewport({ scale });

                    // Create canvas
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Render page to canvas
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert to base64
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    images.push({
                        pageNumber: pageNum,
                        data: imageData
                    });

                } catch (error) {
                    console.error(`Error converting page ${pageNum} to image:`, error);
                }
            }
        } else {
            // Process chunk PDF (all pages)
            const numPages = pdfDoc.numPages;
            for (let pageNum = 1; pageNum <= numPages; pageNum++) {
                try {
                    const page = await pdfDoc.getPage(pageNum);
                    const scale = 2.0; // Higher scale for better text recognition
                    const viewport = page.getViewport({ scale });

                    // Create canvas
                    const canvas = document.createElement('canvas');
                    const context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    // Render page to canvas
                    await page.render({
                        canvasContext: context,
                        viewport: viewport
                    }).promise;

                    // Convert to base64
                    const imageData = canvas.toDataURL('image/png').split(',')[1];
                    images.push({
                        pageNumber: startPage + pageNum - 1, // Adjust page number to original document
                        data: imageData
                    });

                } catch (error) {
                    console.error(`Error converting page ${pageNum} to image:`, error);
                }
            }
        }
    } catch (error) {
        console.error('Error loading PDF chunk:', error);
    }

    return images;
}

async function extractTextWithGemini(pageImages, startPage, endPage) {
    try {
        document.getElementById('currentStatus').textContent = 'Sending images to Gemini AI...';

        // Prepare the parts for Gemini API
        const parts = [
            {
                text: `Extract and rewrite all text content from these PDF pages (${startPage}-${endPage}) in a clear, readable format.

                INSTRUCTIONS:
                - Focus on extracting ALL TEXT CONTENT, not preserving layout
                - Rewrite the text in a clear, understandable manner
                - For tables: Convert table data into readable sentences or bullet points
                - For diagrams/charts: Describe the text content and data in plain language
                - For forms: List the field names and any filled values clearly
                - For complex layouts: Reorganize the text logically for easy reading
                - Use simple markdown formatting (headers, lists, paragraphs)
                - Make the content flow naturally and be easy to understand
                - If text is in multiple columns, combine them into a single readable flow
                - Include all visible text, labels, captions, and data
                - Prioritize clarity and readability over exact formatting
                - Do not create complex tables or preserve exact spacing
                - Process ALL pages completely - do not use ellipsis (...) or skip any content
                - Do not add any commentary or explanations beyond the extracted content

                Goal: Make all text content easily readable and understandable, regardless of original layout complexity.

                Processing ${pageImages.length} pages.`
            }
        ];

        // Add each page image
        pageImages.forEach(image => {
            parts.push({
                inline_data: {
                    mime_type: "image/png",
                    data: image.data
                }
            });
        });

        const requestBody = {
            contents: [{
                parts: parts
            }],
            generationConfig: {
                temperature: 0.2, // Slightly higher for more natural text rewriting
                maxOutputTokens: 8192, // Increased for better text extraction
                topP: 0.9,
                topK: 40
            }
        };

        document.getElementById('currentStatus').textContent = 'AI extracting texts...';

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            let extractedText = data.candidates[0].content.parts[0].text;

            // Clean up problematic formatting
            extractedText = cleanupTextFormatting(extractedText);

            return extractedText;
        } else {
            throw new Error('No text content returned from Gemini API');
        }

    } catch (error) {
        console.error(`Error processing pages ${startPage}-${endPage}:`, error);
        return `\n\n**Error processing pages ${startPage}-${endPage}: ${error.message}**\n\n`;
    }
}

async function generateTitleAndDescription() {
    try {
        document.getElementById('currentStatus').textContent = 'Generating title and description...';

        const prompt = `Based on the following extracted text from a PDF document, generate:
1. A brief, descriptive title (max 50 characters)
2. A concise description (max 200 characters) summarizing the document's content

Return your response in this exact format:
TITLE: [your title here]
DESCRIPTION: [your description here]

Extracted text:
${extractedText.substring(0, 2000)}...`;

        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.3,
                maxOutputTokens: 200
            }
        };

        const response = await fetch(GEMINI_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (response.ok) {
            const data = await response.json();
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                const result = data.candidates[0].content.parts[0].text;

                // Parse the response
                const titleMatch = result.match(/TITLE:\s*(.+)/);
                const descMatch = result.match(/DESCRIPTION:\s*(.+)/);

                if (titleMatch) {
                    document.querySelector('input[name="file_title"]').value = titleMatch[1].trim();
                }
                if (descMatch) {
                    document.querySelector('textarea[name="file_description"]').value = descMatch[1].trim();
                }
            }
        }
    } catch (error) {
        console.error('Error generating title and description:', error);
        // Set default values
        document.querySelector('input[name="file_title"]').value = selectedFile.name.replace('.pdf', '');
        document.querySelector('textarea[name="file_description"]').value = 'PDF document with extracted text content';
    }
}

// Timing and utility functions
function formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    } else {
        return `${seconds}s`;
    }
}

function startTimingUpdates() {
    timingInterval = setInterval(updateTimingDisplay, 1000);
}

function stopTimingUpdates() {
    if (timingInterval) {
        clearInterval(timingInterval);
        timingInterval = null;
    }
}

function updateTimingDisplay() {
    if (!processingStartTime) return;

    const elapsed = Date.now() - processingStartTime;
    document.getElementById('elapsedTime').textContent = formatTime(elapsed);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showError(message) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-danger, .alert-success');
    existingMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-success')) {
            msg.remove();
        }
    });

    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show';
    errorDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(errorDiv, document.querySelector('.card-body').firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

function showSuccess(message) {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.alert-danger, .alert-success');
    existingMessages.forEach(msg => {
        if (msg.classList.contains('alert-danger') || msg.classList.contains('alert-success')) {
            msg.remove();
        }
    });

    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success alert-dismissible fade show';
    successDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.card-body').insertBefore(successDiv, document.querySelector('.card-body').firstChild);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.remove();
        }
    }, 3000);
}

// Step management functions
function activateStep(stepNumber) {
    // Remove active class from all steps
    document.querySelectorAll('.step-item').forEach(step => {
        step.classList.remove('active');
    });

    // Add active class to current step
    const currentStep = document.getElementById(`step${stepNumber}`);
    if (currentStep) {
        currentStep.classList.add('active');
    }
}

function markStepCompleted(stepNumber) {
    const step = document.getElementById(`step${stepNumber}`);
    if (step) {
        step.classList.remove('active');
        step.classList.add('completed');
    }
}

function cleanupTextFormatting(text) {
    if (!text) return text;

    try {
        // Remove excessive empty table rows (more than 5 consecutive)
        text = text.replace(/(\|\s*\|\s*\|\s*\|\s*\|\s*\|\s*\n){6,}/g, '\n*[Multiple empty table rows removed for readability]*\n\n');

        // Remove any remaining problematic table patterns
        text = text.replace(/(\|\s*\|\s*\|\s*\|\s*\|\s*\|\n){10,}/g, '\n*[Repetitive table structure simplified]*\n\n');

        // Clean up excessive pipe characters that might indicate malformed tables
        text = text.replace(/\|{5,}/g, '|');

        // Remove excessive newlines
        text = text.replace(/\n{4,}/g, '\n\n');

        // Clean up any remaining table artifacts that are mostly empty
        text = text.replace(/\|\s*\|\s*\|\s*\|\s*\|\s*\|(\s*\n\|\s*\|\s*\|\s*\|\s*\|\s*\|){5,}/g, '\n*[Empty table structure converted to readable format]*\n');

        // Limit overall response length to prevent token overflow
        if (text.length > 12000) {
            text = text.substring(0, 12000) + '\n\n*[Content truncated - showing first portion of extracted text]*';
        }

        return text.trim();

    } catch (cleanupError) {
        console.error('Error cleaning up text:', cleanupError);
        // If cleanup fails, return original text truncated
        return text.length > 8000 ? text.substring(0, 8000) + '\n\n*[Content truncated due to processing issues]*' : text;
    }
}

// Status update functions
function resetProcessingStatus() {
    const statusItems = ['split', 'image', 'ai', 'complete'];
    const statusTexts = [
        'PDF Splitting: Pending',
        'Image Conversion: Pending',
        'AI Text Extraction: Pending',
        'Processing: Not Started'
    ];

    statusItems.forEach((item, index) => {
        const statusElement = document.getElementById(`${item}Status`);
        const iconElement = document.getElementById(`${item}Icon`);
        const containerElement = statusElement.parentElement;

        if (statusElement) statusElement.textContent = statusTexts[index];
        if (iconElement) {
            iconElement.className = `fas fa-${getIconClass(item)} me-2 text-muted`;
        }
        if (containerElement) {
            containerElement.classList.remove('processing', 'completed');
        }
    });
}

function updateProcessingStatus(type, status, text) {
    const statusElement = document.getElementById(`${type}Status`);
    const iconElement = document.getElementById(`${type}Icon`);
    const containerElement = statusElement.parentElement;

    if (statusElement) {
        statusElement.textContent = text;
    }

    if (containerElement) {
        containerElement.classList.remove('processing', 'completed');
        containerElement.classList.add(status);
    }

    if (iconElement && status === 'completed') {
        iconElement.className = `fas fa-check-circle me-2 text-success`;
    } else if (iconElement && status === 'processing') {
        iconElement.className = `fas fa-spinner fa-spin me-2 text-danger`;
    }
}

function getIconClass(type) {
    const icons = {
        'split': 'cut',
        'image': 'image',
        'ai': 'robot',
        'complete': 'check-circle'
    };
    return icons[type] || 'circle';
}

// Initialize first step as active
document.addEventListener('DOMContentLoaded', function() {
    activateStep(1);
});
</script>
<?= $this->endSection() ?>
